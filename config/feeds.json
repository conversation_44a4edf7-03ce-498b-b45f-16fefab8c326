{"timescaledb": {"host": "localhost", "port": 5432, "user": "postgres", "password": "fundingarb", "database": "fundingarb", "min_connections": 2, "max_connections": 10, "command_timeout": 30}, "exchanges": {"blofin": {"name": "blofin", "enabled": true, "is_demo": false, "update_interval": 10.0}, "hyperliquid": {"name": "hyperliquid", "enabled": true, "api_url": "https://api.hyperliquid.xyz/info", "update_interval": 30.0}, "kraken": {"name": "kraken", "enabled": true, "api_url": "https://futures.kraken.com/derivatives/api/v3/tickers", "update_interval": 300.0}, "mexc": {"name": "mexc", "enabled": true, "contract_details_url": "https://contract.mexc.com/api/v1/contract/detail", "funding_rate_url": "https://contract.mexc.com/api/v1/contract/funding_rate", "update_interval": 300.0}}, "log_level": "INFO", "data_retention_seconds": 3600}