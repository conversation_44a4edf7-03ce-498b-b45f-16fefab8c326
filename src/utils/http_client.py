"""HTTP client utilities for exchange API interactions."""

import asyncio
import logging
from typing import Optional, Dict, Any, Union
import aiohttp
from dataclasses import dataclass


logger = logging.getLogger(__name__)


@dataclass
class HTTPConfig:
    """Configuration for HTTP client."""
    timeout_total: int = 30
    timeout_connect: int = 10
    limit: int = 20
    limit_per_host: int = 10
    ttl_dns_cache: int = 300
    keepalive_timeout: int = 60


class AsyncHTTPClient:
    """Reusable async HTTP client with connection pooling and error handling."""
    
    def __init__(self, config: Optional[HTTPConfig] = None):
        self.config = config or HTTPConfig()
        self.session: Optional[aiohttp.ClientSession] = None
        self._connector: Optional[aiohttp.TCPConnector] = None
        self._connected = False
        
    async def connect(self) -> bool:
        """Initialize HTTP session with connection pooling."""
        try:
            self._connector = aiohttp.TCPConnector(
                limit=self.config.limit,
                limit_per_host=self.config.limit_per_host,
                ttl_dns_cache=self.config.ttl_dns_cache,
                use_dns_cache=True,
                keepalive_timeout=self.config.keepalive_timeout,
                enable_cleanup_closed=True
            )
            
            self.session = aiohttp.ClientSession(
                connector=self._connector,
                timeout=aiohttp.ClientTimeout(
                    total=self.config.timeout_total,
                    connect=self.config.timeout_connect
                )
            )
            
            self._connected = True
            logger.debug("HTTP client initialized with connection pooling")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize HTTP client: {e}")
            return False
    
    async def disconnect(self) -> None:
        """Close HTTP session and connector."""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            
            if self._connector:
                await self._connector.close()
                self._connector = None
                
            self._connected = False
            logger.debug("HTTP client disconnected")
            
        except Exception as e:
            logger.error(f"Error disconnecting HTTP client: {e}")
    
    async def get(self, url: str, **kwargs) -> Optional[Dict[str, Any]]:
        """Make GET request with error handling."""
        if not self.session:
            await self.connect()
            
        try:
            async with self.session.get(url, **kwargs) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.warning(f"GET {url} failed with status {response.status}")
                    return None
                    
        except asyncio.TimeoutError:
            logger.error(f"GET {url} timed out")
            return None
        except Exception as e:
            logger.error(f"GET {url} failed: {e}")
            return None
    
    async def post(self, url: str, json: Optional[Dict] = None, data: Optional[Dict] = None, **kwargs) -> Optional[Dict[str, Any]]:
        """Make POST request with error handling."""
        if not self.session:
            await self.connect()
            
        try:
            async with self.session.post(url, json=json, data=data, **kwargs) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.warning(f"POST {url} failed with status {response.status}")
                    return None
                    
        except asyncio.TimeoutError:
            logger.error(f"POST {url} timed out")
            return None
        except Exception as e:
            logger.error(f"POST {url} failed: {e}")
            return None
    
    async def test_connection(self, url: str, method: str = 'GET', **kwargs) -> bool:
        """Test connection to a specific URL."""
        if not self.session:
            await self.connect()
        
        try:
            if method.upper() == 'GET':
                async with self.session.get(url, timeout=aiohttp.ClientTimeout(total=10), **kwargs) as response:
                    return response.status == 200
            else:
                async with self.session.post(url, timeout=aiohttp.ClientTimeout(total=10), **kwargs) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.error(f"Connection test to {url} failed: {e}")
            return False
    
    @property 
    def is_connected(self) -> bool:
        """Check if client is connected."""
        return self._connected and self.session is not None
    
    async def __aenter__(self):
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.disconnect()