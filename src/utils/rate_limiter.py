"""Rate limiting utilities for API requests."""

import asyncio
import time
from typing import List, Dict, Any
from dataclasses import dataclass


@dataclass
class RateLimitRule:
    """Defines a rate limiting rule."""
    max_requests: int
    time_window: float  # in seconds
    min_interval: float = 0.0  # minimum interval between requests


class RateLimiter:
    """Async rate limiter for API requests."""
    
    def __init__(self, rules: List[RateLimitRule]):
        self.rules = rules
        self.request_times: List[float] = []
        self._lock = asyncio.Lock()
    
    async def acquire(self) -> None:
        """Acquire permission to make a request, blocking if necessary."""
        async with self._lock:
            current_time = time.time()
            
            # Apply each rule
            for rule in self.rules:
                await self._apply_rule(rule, current_time)
            
            # Record this request
            self.request_times.append(current_time)
            
            # Clean up old timestamps to prevent memory growth
            self._cleanup_old_requests(current_time)
    
    async def _apply_rule(self, rule: RateLimitRule, current_time: float) -> None:
        """Apply a specific rate limiting rule."""
        # Filter requests within the time window
        recent_requests = [t for t in self.request_times if current_time - t < rule.time_window]
        
        # Check if we need to wait
        if len(recent_requests) >= rule.max_requests:
            # Find the oldest request in the window
            oldest_request = min(recent_requests)
            wait_time = rule.time_window - (current_time - oldest_request)
            
            if wait_time > 0:
                await asyncio.sleep(wait_time)
                return await self._apply_rule(rule, time.time())
        
        # Check minimum interval
        if rule.min_interval > 0 and self.request_times:
            last_request = self.request_times[-1]
            time_since_last = current_time - last_request
            
            if time_since_last < rule.min_interval:
                wait_time = rule.min_interval - time_since_last
                await asyncio.sleep(wait_time)
    
    def _cleanup_old_requests(self, current_time: float) -> None:
        """Remove old request timestamps to prevent memory growth."""
        max_window = max(rule.time_window for rule in self.rules)
        cutoff_time = current_time - max_window - 60  # Keep extra 60 seconds buffer
        
        self.request_times = [t for t in self.request_times if t > cutoff_time]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get rate limiter statistics."""
        current_time = time.time()
        stats = {
            'total_requests': len(self.request_times),
            'rules': []
        }
        
        for rule in self.rules:
            recent_requests = [t for t in self.request_times if current_time - t < rule.time_window]
            stats['rules'].append({
                'max_requests': rule.max_requests,
                'time_window': rule.time_window,
                'current_requests': len(recent_requests),
                'available_slots': rule.max_requests - len(recent_requests)
            })
        
        return stats


class MultiRateLimiter:
    """Manages multiple rate limiters for different API endpoints."""
    
    def __init__(self):
        self.limiters: Dict[str, RateLimiter] = {}
    
    def add_limiter(self, name: str, rules: List[RateLimitRule]) -> None:
        """Add a rate limiter for a specific endpoint/operation."""
        self.limiters[name] = RateLimiter(rules)
    
    async def acquire(self, limiter_name: str) -> None:
        """Acquire permission from a specific rate limiter."""
        if limiter_name in self.limiters:
            await self.limiters[limiter_name].acquire()
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all rate limiters."""
        return {name: limiter.get_stats() for name, limiter in self.limiters.items()}


# Predefined rate limit configurations for common exchanges
class ExchangeRateLimits:
    """Predefined rate limit configurations for common exchanges."""
    
    @staticmethod
    def mexc() -> MultiRateLimiter:
        """MEXC rate limiting configuration."""
        limiter = MultiRateLimiter()
        
        # Funding rates: 20 requests per 2 seconds
        limiter.add_limiter('funding_rates', [
            RateLimitRule(max_requests=20, time_window=2.0)
        ])
        
        # Contract details: 1 request per 5 seconds
        limiter.add_limiter('contract_details', [
            RateLimitRule(max_requests=1, time_window=5.0, min_interval=5.0)
        ])
        
        return limiter
    
    @staticmethod
    def kraken() -> MultiRateLimiter:
        """Kraken rate limiting configuration (conservative)."""
        limiter = MultiRateLimiter()
        
        # General API: 1 request per second (conservative)
        limiter.add_limiter('general', [
            RateLimitRule(max_requests=60, time_window=60.0, min_interval=1.0)
        ])
        
        return limiter
    
    @staticmethod
    def hyperliquid() -> MultiRateLimiter:
        """Hyperliquid rate limiting configuration."""
        limiter = MultiRateLimiter()
        
        # General API: 10 requests per second
        limiter.add_limiter('general', [
            RateLimitRule(max_requests=10, time_window=1.0)
        ])
        
        return limiter