"""Centralized asynchronous WebSocket manager.

Design goals:
- One place to manage connections, subscriptions, heartbeats, and reconnects
- Per-exchange adapter layer for auth, URL, subscribe/unsubscribe messages, and message parsing
- Easy to add new WS endpoints by implementing thin adapters
- Provide hooks to push parsed data to TimescaleDB
"""

from __future__ import annotations

import asyncio
import json
import logging
import time
import sys
import os
from dataclasses import dataclass
from typing import Any, AsyncIterator, Callable, Dict, List, Optional

import websockets

# Ensure src is on the path for absolute-style imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from timescaledb_client import TimescaleDBClient

logger = logging.getLogger(__name__)


@dataclass
class WSSubscription:
    key: str  # unique per exchange (e.g., "funding:BTC-USDT")
    build_sub_msg: Callable[[], Dict[str, Any] | str]
    build_unsub_msg: Optional[Callable[[], Dict[str, Any] | str]] = None


class WSAdapter:
    """Exchange-specific adapter contract."""

    name: str

    def __init__(self, name: str):
        self.name = name

    def ws_url(self) -> str:
        raise NotImplementedError

    async def authenticate(self, ws) -> bool:
        return True

    def heartbeat_msg(self) -> Optional[Dict[str, Any] | str]:
        return None

    def parse_message(self, raw: Dict[str, Any] | List[Any] | Any) -> List[Dict[str, Any]]:
        """Return a list of funding payloads ready for DB, each containing instrument and funding_rate.
        Example: [{"instrument": "BTC-USDT", "funding_rate": 0.0001, ...}]
        """
        raise NotImplementedError


class WebsocketManager:
    def __init__(self, db: TimescaleDBClient):
        self._db = db
        self._adapters: Dict[str, WSAdapter] = {}
        self._subs: Dict[str, List[WSSubscription]] = {}
        self._tasks: Dict[str, asyncio.Task] = {}
        self._running = False

    def register_exchange(self, adapter: WSAdapter) -> None:
        self._adapters[adapter.name] = adapter
        self._subs.setdefault(adapter.name, [])

    def add_subscription(self, exchange: str, sub: WSSubscription) -> None:
        if exchange not in self._adapters:
            raise ValueError(f"Exchange '{exchange}' is not registered")
        self._subs[exchange].append(sub)

    async def start(self) -> None:
        if self._running:
            return
        self._running = True
        for name in self._adapters.keys():
            task = asyncio.create_task(self._run_exchange(name), name=f"ws:{name}")
            self._tasks[name] = task
        logger.info(f"WebsocketManager started for {len(self._tasks)} exchanges")

    async def stop(self) -> None:
        if not self._running:
            return
        self._running = False
        for t in self._tasks.values():
            t.cancel()
        await asyncio.gather(*self._tasks.values(), return_exceptions=True)
        self._tasks.clear()
        logger.info("WebsocketManager stopped")

    async def _run_exchange(self, name: str) -> None:
        adapter = self._adapters[name]
        subs = self._subs.get(name, [])
        url = adapter.ws_url()

        backoff = 1.0
        while self._running:
            try:
                async with websockets.connect(url, ping_interval=20, ping_timeout=10) as ws:
                    # auth if needed
                    if not await adapter.authenticate(ws):
                        raise RuntimeError("auth failed")

                    # subscribe
                    for s in subs:
                        await self._send(ws, s.build_sub_msg())

                    last_pong = time.time()
                    heartbeat_msg = adapter.heartbeat_msg()

                    async for msg in ws:
                        last_pong = time.time()
                        try:
                            raw = json.loads(msg) if isinstance(msg, (str, bytes)) else msg
                        except Exception:
                            raw = msg
                        try:
                            payloads = adapter.parse_message(raw)
                            for p in payloads:
                                instr = p.get("instrument")
                                if not instr:
                                    continue
                                await self._db.store_funding_rate(adapter.name, instr, p)
                        except Exception as e:
                            logger.debug(f"WS parse error [{name}]: {e}")

                        # Send periodic heartbeat
                        if heartbeat_msg and (time.time() - last_pong) > 20:
                            await self._send(ws, heartbeat_msg)

                    backoff = 1.0  # clean close -> reset
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"WS error for {name}: {e}; reconnecting in {backoff:.1f}s")
                await asyncio.sleep(backoff)
                backoff = min(30.0, backoff * 2)

    async def _send(self, ws, msg: Dict[str, Any] | str) -> None:
        if isinstance(msg, (dict, list)):
            await ws.send(json.dumps(msg))
        else:
            await ws.send(msg)

