"""Centralized asynchronous REST manager for exchange endpoints.

Design goals:
- One place to schedule/poll REST endpoints across exchanges
- Per-exchange rate limiting via MultiRateLimiter
- Easy to add new endpoints via simple descriptors
- Standardized push to TimescaleDB
"""

from __future__ import annotations

import asyncio
import logging
import time
import sys
import os
from dataclasses import dataclass, field
from typing import Any, Awaitable, Callable, Dict, List, Optional, Tuple

# Ensure src is on the path for absolute-style imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.http_client import AsyncHTTPClient, HTTPConfig
from utils.rate_limiter import MultiRateLimiter
from timescaledb_client import TimescaleDBClient


logger = logging.getLogger(__name__)

# Type aliases
RequestBuilder = Callable[[], Dict[str, Any]]
FanoutRequestBuilder = Callable[[], List[Tuple[str, str, Dict[str, Any]]]]  # list of (method, url, kwargs)
ResponseParser = Callable[[Dict[str, Any] | List[Any] | Any], Dict[str, Dict[str, Any]]]


@dataclass
class RestEndpoint:
    """Descriptor for a polled REST endpoint.

    - name: unique key for logging/metrics
    - method: 'GET' | 'POST'
    - url: full URL
    - interval: seconds between polls
    - rate_limit_key: name of limiter in the exchange's MultiRateLimiter
    - build_request: returns kwargs for HTTP call (params/json/data/headers)
    - parse_response: turns raw JSON into mapping: instrument -> funding data dict
      The funding data dict should at least contain 'funding_rate' and may include
      'funding_time', 'collection_time', 'maker_fee', 'taker_fee', 'funding_collection_hours', ...
    """

    name: str
    method: str
    url: str
    interval: float
    rate_limit_key: Optional[str]
    build_request: Optional[RequestBuilder] = None
    build_requests: Optional[FanoutRequestBuilder] = None  # optional fan-out
    fanout_batch_size: int = 18  # default safe batch for MEXC 20/2s
    parse_response: Optional[ResponseParser] = None
    enabled: bool = True


@dataclass
class RegisteredExchange:
    name: str
    rate_limiter: Optional[MultiRateLimiter] = None
    default_maker_fee: Optional[float] = None
    default_taker_fee: Optional[float] = None
    endpoints: List[RestEndpoint] = field(default_factory=list)


class RestManager:
    """Centralized orchestrator for REST polling across exchanges."""

    def __init__(self, db: TimescaleDBClient, http_config: Optional[HTTPConfig] = None):
        self._db = db
        self._http = AsyncHTTPClient(http_config)
        self._exchanges: Dict[str, RegisteredExchange] = {}
        self._tasks: List[asyncio.Task] = []
        self._running = False

    async def start(self) -> None:
        if self._running:
            return
        await self._http.connect()
        self._running = True
        # launch endpoint pollers
        for exch in self._exchanges.values():
            for ep in exch.endpoints:
                if not ep.enabled:
                    continue
                task = asyncio.create_task(self._run_endpoint_loop(exch, ep), name=f"rest:{exch.name}:{ep.name}")
                self._tasks.append(task)
        logger.info(f"RestManager started with {len(self._tasks)} endpoint tasks")

    async def stop(self) -> None:
        if not self._running:
            return
        self._running = False
        for t in self._tasks:
            t.cancel()
        await asyncio.gather(*self._tasks, return_exceptions=True)
        self._tasks.clear()
        # http client context manager closes session on exit; be explicit
        if self._http.session:
            await self._http.disconnect()
        logger.info("RestManager stopped")

    def register_exchange(
        self,
        name: str,
        rate_limiter: Optional[MultiRateLimiter] = None,
        default_maker_fee: Optional[float] = None,
        default_taker_fee: Optional[float] = None,
    ) -> None:
        self._exchanges[name] = RegisteredExchange(
            name=name,
            rate_limiter=rate_limiter,
            default_maker_fee=default_maker_fee,
            default_taker_fee=default_taker_fee,
        )
        logger.debug(f"Registered exchange: {name}")

    def add_endpoint(self, exchange: str, endpoint: RestEndpoint) -> None:
        if exchange not in self._exchanges:
            raise ValueError(f"Exchange '{exchange}' is not registered")
        self._exchanges[exchange].endpoints.append(endpoint)
        logger.debug(f"Added endpoint {endpoint.name} to {exchange}")

    async def _run_endpoint_loop(self, exch: RegisteredExchange, ep: RestEndpoint) -> None:
        """Continuously poll an endpoint with rate limiting and backoff."""
        interval = max(0.1, ep.interval)
        error_count = 0
        while self._running and ep.enabled:
            start = time.time()
            try:
                # Fan-out mode: build multiple requests per cycle
                if ep.build_requests is not None:
                    reqs = ep.build_requests()
                    # Launch all with per-request rate limit honoring
                    results: List[Dict[str, Any]] = []

                    async def do_one(method: str, url: str, kwargs: Dict[str, Any]) -> None:
                        nonlocal results
                        # Extract meta and filter kwargs for aiohttp
                        meta = kwargs.pop('_meta', None)
                        http_kwargs = {k: v for k, v in kwargs.items() if k in ('params', 'json', 'data', 'headers')}
                        # Rate limit per request
                        if exch.rate_limiter and ep.rate_limit_key:
                            await exch.rate_limiter.acquire(ep.rate_limit_key)
                        resp = await (self._http.get(url, **http_kwargs) if method.upper() == 'GET' else self._http.post(url, **http_kwargs))
                        results.append({'meta': meta, 'resp': resp})

                    await asyncio.gather(*[do_one(m, u, kw) for (m, u, kw) in reqs])

                    # Parse composite results if parser provided
                    data_map: Dict[str, Dict[str, Any]] = {}
                    if ep.parse_response:
                        data_map = ep.parse_response(results)  # type: ignore[arg-type]
                    # Enrich and store
                    if data_map:
                        await self._store_funding_batch(exch, data_map)

                else:
                    # Single-request mode with endpoint-level rate limit
                    if exch.rate_limiter and ep.rate_limit_key:
                        await exch.rate_limiter.acquire(ep.rate_limit_key)

                    kwargs = ep.build_request() if ep.build_request else {}
                    method = ep.method.upper()

                    # Execute
                    resp = await (self._http.get(ep.url, **kwargs) if method == 'GET' else self._http.post(ep.url, **kwargs))

                    if resp is None:
                        raise RuntimeError("Empty response")

                    # Parse -> mapping instrument -> funding data
                    data_map: Dict[str, Dict[str, Any]]
                    if ep.parse_response:
                        data_map = ep.parse_response(resp)
                    else:
                        # If no parser, assume resp is already a mapping
                        if isinstance(resp, dict):
                            data_map = resp  # type: ignore
                        else:
                            raise ValueError("parse_response missing and response is not a dict")

                    # Enrich and store
                    await self._store_funding_batch(exch, data_map)

                error_count = 0
            except asyncio.CancelledError:
                break
            except Exception as e:
                error_count += 1
                logger.warning(f"REST loop error [{exch.name}:{ep.name}]: {e}")
            finally:
                # Adaptive sleep with mild backoff on repeated errors
                elapsed = time.time() - start
                sleep_s = max(0.0, interval - elapsed)
                if error_count:
                    sleep_s *= min(3.0, 1.0 + 0.5 * error_count)
                await asyncio.sleep(sleep_s)

    async def _store_funding_batch(self, exch: RegisteredExchange, data_map: Dict[str, Dict[str, Any]]) -> None:
        for instrument, payload in data_map.items():
            # Set defaults if adapter didn't
            payload.setdefault("collection_time", time.time())
            if exch.default_maker_fee is not None and "maker_fee" not in payload:
                payload["maker_fee"] = exch.default_maker_fee
            if exch.default_taker_fee is not None and "taker_fee" not in payload:
                payload["taker_fee"] = exch.default_taker_fee
            await self._db.store_funding_rate(exch.name, instrument, payload)


# Adapter API
class BaseRestAdapter:
    """Minimal interface for exchange-specific REST wiring.

    Implementations should:
    - Provide exchange name
    - Provide MultiRateLimiter (if needed)
    - Optionally perform bootstrap (e.g., fetch contracts) in `bootstrap`
    - Return a list of RestEndpoint descriptors in `endpoints`
    - Optionally set default maker/taker fees (from DB or config)
    """

    name: str

    def __init__(self, name: str):
        self.name = name

    def get_rate_limiter(self) -> Optional[MultiRateLimiter]:
        return None

    async def bootstrap(self, rest: RestManager, db: TimescaleDBClient) -> None:
        return None

    def endpoints(self) -> List[RestEndpoint]:
        raise NotImplementedError

    async def register_with(self, rest: RestManager, db: TimescaleDBClient) -> None:
        # Defaults from DB if present
        default_maker = None
        default_taker = None
        try:
            fees = await db.get_exchange_fees(self.name)
            if fees:
                default_maker = fees.get("default_maker_fee")
                default_taker = fees.get("default_taker_fee")
        except Exception:
            pass

        rest.register_exchange(
            name=self.name,
            rate_limiter=self.get_rate_limiter(),
            default_maker_fee=default_maker,
            default_taker_fee=default_taker,
        )
        await self.bootstrap(rest, db)
        for ep in self.endpoints():
            rest.add_endpoint(self.name, ep)

