"""Kraken REST adapter for centralized RestManager.

Fetches all perpetual futures via tickers endpoint and derives funding rate per base asset.
"""
from __future__ import annotations

from typing import Any, Dict, List, Optional

from utils.rate_limiter import ExchangeRateLimits
from core.rest_manager import BaseRestAdapter, RestEndpoint


class KrakenRestAdapter(BaseRestAdapter):
    def __init__(self, api_url: str):
        super().__init__(name="kraken")
        self.api_url = api_url

    def get_rate_limiter(self):
        return ExchangeRateLimits.kraken()

    def endpoints(self) -> List[RestEndpoint]:
        return [
            RestEndpoint(
                name="tickers",
                method="GET",
                url=self.api_url,
                interval=60.0,
                rate_limit_key="general",
                parse_response=self._parse_tickers,
            )
        ]

    def _parse_tickers(self, data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        # Kraken response format per existing feed
        if not isinstance(data, dict) or "tickers" not in data:
            return {}
        tickers = data.get("tickers") or []
        out: Dict[str, Dict[str, Any]] = {}
        for t in tickers:
            if t.get("tag") != "perpetual":
                continue
            symbol = t.get("symbol", "")
            pair = t.get("pair", "")
            abs_funding = t.get("fundingRate")
            mark_price = t.get("markPrice") or 0
            if not symbol or not mark_price:
                continue
            try:
                funding_rate = float(abs_funding) / float(mark_price)
            except Exception:
                continue
            base_asset = None
            if ":" in pair:
                base_asset = pair.split(":")[0]
            elif symbol.startswith("PF_") and symbol.endswith("USD"):
                base_asset = symbol[3:-3]
            if not base_asset:
                continue
            instr = base_asset.upper()
            out[instr] = {
                "funding_rate": float(funding_rate),
                "funding_collection_hours": 1,
                "symbol": symbol,
                "pair": pair,
                "mark_price": t.get("markPrice"),
                "index_price": t.get("indexPrice"),
                "open_interest": t.get("openInterest"),
                "vol_24h": t.get("vol24h"),
                "volume_quote": t.get("volumeQuote"),
                "funding_rate_prediction": t.get("fundingRatePrediction"),
                "bid": t.get("bid"),
                "ask": t.get("ask"),
                "last": t.get("last"),
                "suspended": t.get("suspended"),
                "raw_data": t,
            }
        return out

