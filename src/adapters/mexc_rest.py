"""MEXC REST adapter for centralized RestManager.

Implements contract bootstrap (cached to TimescaleDB) and batched funding rate fetches with rate limiting.
"""
from __future__ import annotations

import logging
import time
from typing import Any, Dict, List, Optional

from utils.rate_limiter import ExchangeRateLimits
from core.rest_manager import BaseRestAdapter, RestEndpoint, RestManager
from timescaledb_client import TimescaleDBClient


logger = logging.getLogger(__name__)


class MexcRestAdapter(BaseRestAdapter):
    def __init__(self, contract_details_url: str, funding_rate_url: str):
        super().__init__(name="mexc")
        self.contract_details_url = contract_details_url
        self.funding_rate_url = funding_rate_url.rstrip("/")
        self._contracts: Dict[str, Dict[str, Any]] = {}
        self._last_contract_fetch = 0.0

    def get_rate_limiter(self):
        return ExchangeRateLimits.mexc()

    async def bootstrap(self, rest: RestManager, db: TimescaleDBClient) -> None:
        # Load from DB cache if fresh
        now = time.time()
        cached = await db.get_contract_details("mexc", max_age_hours=24)
        if cached:
            self._contracts = cached
            self._last_contract_fetch = now
            return

        # TODO:
        # Let the first requests fail gracefully due to lack of cached contracts.
        # We need to add https://contract.mexc.com/api/v1/contract/risk_reverse as a RestEndpoint.
        # This endpoint contains only the names of all USDT contracts like this:
        # {"success":true,"code":0,"data":[{"symbol":"BTC_USDT","currency":"USDT","available":95440965.252130079260819526360404298738,"timestamp":1756394374599},{"symbol":"ETH_USDT","currency":"USDT","available":53092028.273161779069026422740462420441,"timestamp":1756394374599},{"symbol":"SOL_USDT","currency":"USDT","available":35594813.422114659674557307985153955435,"timestamp":1756394374599},
        return None

    def endpoints(self) -> List[RestEndpoint]:
        # Two kinds of usage:
        # 1) Periodically refresh contract details (low-frequency)
        # 2) High-rate funding rates per-contract in manageable batches
        return [
            RestEndpoint(
                name="mexc_contract_details",
                method="GET",
                url=self.contract_details_url,
                interval=3600.0,  # hourly refresh
                rate_limit_key="contract_details",
                parse_response=self._parse_and_cache_contracts,
            ),
            RestEndpoint(
                name="mexc_funding_rates_batch",
                method="GET",
                url=self.funding_rate_url,  # base; fan-out uses per-symbol URLs
                interval=300.0,
                rate_limit_key="funding_rates",
                build_requests=self._build_requests,
                parse_response=self._parse_fanout_results,
                fanout_batch_size=18,
            ),
        ]

    def _parse_and_cache_contracts(self, data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        # Response shape: { success: bool, data: [ { symbol, ... } ] }
        if not isinstance(data, dict) or not data.get("success"):
            return {}
        new_contracts: Dict[str, Dict[str, Any]] = {}
        for c in data.get("data", []) or []:
            sym = c.get("symbol")
            if sym.endswith('_USDT'):
                new_contracts[sym] = c
        if new_contracts:
            self._contracts = new_contracts
            self._last_contract_fetch = time.time()
        # No funding rows to insert from this endpoint
        return {}

    def _build_requests(self) -> List[tuple]:
        # Build per-symbol GET requests; attach symbol in _meta for parsing
        reqs: List[tuple] = []
        symbols = list(self._contracts.keys())
        if not symbols:
            logger.warning(f"No contracts cached for MEXC; cannot build funding rate requests.")

        # Keep concurrency safe by letting rate limiter gate requests; manager will gather all
        for sym in symbols:
            url = f"{self.funding_rate_url}/{sym}"
            reqs.append(("GET", url, {"_meta": {"symbol": sym}}))
        return reqs

    def _parse_fanout_results(self, results: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        out: Dict[str, Dict[str, Any]] = {}
        for item in results:
            meta = item.get("meta") or {}
            sym = meta.get("symbol")
            resp = item.get("resp") or {}
            if not sym or not isinstance(resp, dict) or not resp.get("success"):
                continue
            funding_info = resp.get("data", {})
            contract_details = self._contracts.get(sym, {})
            funding_rate = funding_info.get("fundingRate")
            if funding_rate is None:
                continue
            maker_fee = contract_details.get("makerFeeRate", 0.0)
            taker_fee = contract_details.get("takerFeeRate", 0.0)
            collect_cycle = funding_info.get("collectCycle", 8)
            out[sym] = {
                "funding_rate": float(funding_rate),
                "funding_time": funding_info.get("nextSettleTime"),
                "max_funding_rate": funding_info.get("maxFundingRate"),
                "min_funding_rate": funding_info.get("minFundingRate"),
                "collect_cycle": collect_cycle,
                "funding_collection_hours": collect_cycle,
                "maker_fee": float(maker_fee),
                "taker_fee": float(taker_fee),
                "contract_size": contract_details.get("contractSize"),
                "price_scale": contract_details.get("priceScale"),
                "vol_scale": contract_details.get("volScale"),
                "raw_funding_data": funding_info,
                "raw_contract_data": contract_details,
            }
        return out

