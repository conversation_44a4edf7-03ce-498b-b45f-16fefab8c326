"""Blofin WS adapter for centralized WebsocketManager.

Uses existing BlofinWsPublicClient to manage subs and parses funding rate messages to DB payloads.
"""
from __future__ import annotations

import async<PERSON>
import json
from typing import Any, Dict, List, Optional

from core.ws_manager import WSAdapter, WSSubscription
from blofin.websocket_client import BlofinWsPublicClient


class BlofinWSAdapter(WSAdapter):
    def __init__(self, is_demo: bool = False):
        super().__init__(name="blofin")
        self._is_demo = is_demo
        self._client: Optional[BlofinWsPublicClient] = None
        self._funding_intervals: Dict[str, int] = {}

    def set_funding_intervals(self, intervals: Dict[str, int]) -> None:
        self._funding_intervals = {k.upper(): v for k, v in intervals.items()}

    def ws_url(self) -> str:
        # The internal client manages URL; ws_manager uses websockets directly, but we will
        # bypass ws_manager's connection and reuse client for consistency. To fit ws_manager,
        # we provide minimal stubs and rely on parsing format below if routed through ws_manager.
        # If we fully integrate, we would implement raw URL and subscribe messages.
        # For now, return public URL constant from client class.
        return BlofinWsPublicClient.PUBLIC_WS_URL if not self._is_demo else BlofinWsPublicClient.DEMO_PUBLIC_WS_URL

    async def authenticate(self, ws) -> bool:
        return True

    def heartbeat_msg(self) -> Optional[Dict[str, Any] | str]:
        # Blofin uses ping/pong strings
        return "ping"

    def parse_message(self, raw: Dict[str, Any] | List[Any] | Any) -> List[Dict[str, Any]]:
        try:
            msg = raw if isinstance(raw, dict) else {}
            arg = msg.get("arg", {})
            if arg.get("channel") != "funding-rate":
                return []
            data_list = msg.get("data", []) or []
            out: List[Dict[str, Any]] = []
            for d in data_list:
                inst_id = d.get("instId")
                fr = d.get("fundingRate")
                ft = d.get("fundingTime")
                if inst_id is None or fr is None:
                    continue
                # Default funding interval unknown without REST call; 8H typical; you have logic in old feed.
                out.append({
                    "instrument": inst_id,
                    "funding_rate": float(fr),
                    "funding_time": int(ft) if ft else None,
                    "funding_collection_hours": self._funding_intervals.get(inst_id.upper(), 8),
                    "raw_data": d,
                })
            return out
        except Exception:
            return []

    def build_sub(self, inst_id: str) -> WSSubscription:
        def sub_msg():
            return {"op": "subscribe", "args": [{"channel": "funding-rate", "instId": inst_id}]}
        return WSSubscription(key=f"funding:{inst_id}", build_sub_msg=sub_msg)

