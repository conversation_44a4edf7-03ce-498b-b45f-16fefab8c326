"""Hyperliquid REST adapter for the centralized RestManager.

Mirrors previous HyperliquidFeed logic using a single POST endpoint returning
meta and asset contexts; parses funding data for all instruments.
"""
from __future__ import annotations

from typing import Any, Dict, List, Optional

from utils.rate_limiter import ExchangeRateLimits
from core.rest_manager import BaseRestAdapter, RestEndpoint, RestManager
from timescaledb_client import TimescaleDBClient


class HyperliquidRestAdapter(BaseRestAdapter):
    def __init__(self, api_url: str):
        super().__init__(name="hyperliquid")
        self.api_url = api_url

    def get_rate_limiter(self):
        return ExchangeRateLimits.hyperliquid()

    def endpoints(self) -> List[RestEndpoint]:
        return [
            RestEndpoint(
                name="metaAndAssetCtxs",
                method="POST",
                url=self.api_url,
                interval=30.0,
                rate_limit_key="general",
                build_request=lambda: {"json": {"type": "metaAndAssetCtxs"}},
                parse_response=self._parse_meta_and_ctx,
            )
        ]

    def _parse_meta_and_ctx(self, data: List[Any] | Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        # Expected format: [ { universe: [...] }, [ctxs...] ]
        if not isinstance(data, list) or len(data) < 2:
            return {}
        universe_list = data[0].get("universe", []) if isinstance(data[0], dict) else []
        contexts_list = data[1]
        out: Dict[str, Dict[str, Any]] = {}

        for info, context in zip(universe_list, contexts_list):
            instrument_name = (info.get("name", "") if isinstance(info, dict) else "").upper()
            if not instrument_name:
                continue
            funding = context.get("funding") if isinstance(context, dict) else None
            try:
                funding_rate = float(funding) if funding is not None else None
            except (TypeError, ValueError):
                funding_rate = None
            if funding_rate is None:
                continue

            out[instrument_name] = {
                "funding_rate": funding_rate,
                "funding_collection_hours": 1,
                "open_interest": context.get("openInterest"),
                "prev_day_px": context.get("prevDayPx"),
                "day_ntl_vlm": context.get("dayNtlVlm"),
                "premium": context.get("premium"),
                "oracle_px": context.get("oraclePx"),
                "mark_px": context.get("markPx"),
                "mid_px": context.get("midPx"),
                "day_base_vlm": context.get("dayBaseVlm"),
                "raw_data": context,
            }
        return out

    async def bootstrap(self, rest: RestManager, db: TimescaleDBClient) -> None:
        # Optionally fetch available contracts first via another endpoint if needed.
        # Hyperliquid doesn't require pre-fetch for funding, so skip.
        return None

