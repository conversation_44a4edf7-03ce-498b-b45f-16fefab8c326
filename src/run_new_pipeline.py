"""Runner to start centralized REST and WS managers using config.

This replaces old FeedManager-based architecture.
"""
from __future__ import annotations

import asyncio
import logging

from src.config import Config
from src.timescaledb_client import TimescaleDBClient
from src.core.rest_manager import RestManager
from src.core.ws_manager import WebsocketManager
from src.adapters.hyperliquid_rest import HyperliquidRestAdapter
from src.adapters.kraken_rest import KrakenRestAdapter
from src.adapters.mexc_rest import MexcRestAdapter
from src.adapters.blofin_ws import BlofinWSAdapter


async def main():
    cfg = Config().load()

    logging.basicConfig(level=getattr(logging, cfg.log_level.upper(), logging.INFO))

    db = TimescaleDBClient(cfg.timescaledb)
    assert await db.connect(), "Failed to connect DB"

    rest = RestManager(db)
    ws = WebsocketManager(db)

    # Register REST adapters
    ex_cfgs = cfg.exchanges
    if ex_cfgs.get("hyperliquid") and ex_cfgs["hyperliquid"].enabled:
        hl = HyperliquidRestAdapter(ex_cfgs["hyperliquid"].api_url)
        await hl.register_with(rest, db)
    if ex_cfgs.get("kraken") and ex_cfgs["kraken"].enabled:
        kr = KrakenRestAdapter(ex_cfgs["kraken"].api_url)
        await kr.register_with(rest, db)
    if ex_cfgs.get("mexc") and ex_cfgs["mexc"].enabled:
        mx = MexcRestAdapter(
            ex_cfgs["mexc"].contract_details_url,
            ex_cfgs["mexc"].funding_rate_url,
        )
        await mx.register_with(rest, db)

    # Register WS adapters
    if ex_cfgs.get("blofin") and ex_cfgs["blofin"].enabled:
        blofin_adapter = BlofinWSAdapter(is_demo=ex_cfgs["blofin"].is_demo)
        ws.register_exchange(blofin_adapter)
        # Fetch all contracts via Blofin REST and per-pair funding intervals
        from blofin.client import Client
        from blofin.rest_market import MarketAPI
        import aiohttp
        rest_client = Client(isDemo=ex_cfgs["blofin"].is_demo)
        market = MarketAPI(rest_client)
        instruments = market.getInstruments()
        if instruments.get("code") == "0":
            # Best-effort fetch funding intervals per pair
            intervals = {}
            async def fetch_interval(session, inst_id: str):
                url = f'https://blofin.com/uapi/v1/basic/contract/info?symbol={inst_id}'
                try:
                    async with session.get(url) as resp:
                        if resp.status == 200:
                            data = await resp.json()
                            intervals[inst_id] = (data.get('data', {}) or {}).get('funding_interval', 8)
                except Exception:
                    pass
            async with aiohttp.ClientSession() as session:
                await asyncio.gather(*[fetch_interval(session, it.get('instId')) for it in instruments.get('data', []) if it.get('instId')])
            blofin_adapter.set_funding_intervals(intervals)
            for it in instruments.get("data", []):
                inst_id = it.get("instId")
                if inst_id:
                    from core.ws_manager import WSSubscription
                    sub = WSSubscription(
                        key=f"funding:{inst_id}",
                        build_sub_msg=lambda inst_id=inst_id: {"op": "subscribe", "args": [{"channel": "funding-rate", "instId": inst_id}]},
                    )
                    ws.add_subscription("blofin", sub)

    # Start managers
    await asyncio.gather(rest.start(), ws.start())

    try:
        # Run until cancelled
        while True:
            await asyncio.sleep(60)
    except (KeyboardInterrupt, asyncio.CancelledError):
        pass
    finally:
        await asyncio.gather(rest.stop(), ws.stop(), db.disconnect())


if __name__ == "__main__":
    asyncio.run(main())

