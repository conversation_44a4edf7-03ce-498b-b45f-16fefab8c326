"""TimescaleDB client for storing funding rate data."""

import json
import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from decimal import Decimal
import asyncpg
from config import TimescaleDBConfig


logger = logging.getLogger(__name__)


class TimescaleDBClient:
    """Async TimescaleDB client for funding rate data storage."""
    
    def __init__(self, config: TimescaleDBConfig):
        self.config = config
        self.pool: Optional[asyncpg.Pool] = None
        self._connected = False
        self._batch_queue = []
        self._batch_size = 50  # Batch insert size
        self._batch_timeout = 5.0  # Max seconds to wait before batch insert
    
    async def connect(self) -> bool:
        """Connect to TimescaleDB."""
        try:
            print(self.config)
            self.pool = await asyncpg.create_pool(
                host=self.config.host,
                port=self.config.port,
                user=self.config.user,
                password=self.config.password,
                database=self.config.database,
                min_size=self.config.min_connections,
                max_size=self.config.max_connections,
                command_timeout=self.config.command_timeout
            )
            
            # Test connection
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            
            self._connected = True
            logger.info(f"Connected to TimescaleDB at {self.config.host}:{self.config.port}/{self.config.database}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to TimescaleDB: {e}")
            self._connected = False
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from TimescaleDB."""
        # Flush any pending batch data
        if self._batch_queue:
            await self._process_batch()
        
        if self.pool:
            await self.pool.close()
            self._connected = False
            logger.info("Disconnected from TimescaleDB")
    
    async def store_funding_rate(self, exchange: str, instrument: str, data: Dict[str, Any]) -> bool:
        """
        Store funding rate data for an instrument using batching for better performance.
        
        Args:
            exchange: Exchange name (e.g., 'blofin', 'hyperliquid')
            instrument: Instrument symbol (e.g., 'BTC-USDT', 'BTC')
            data: Funding rate data dictionary
            
        Returns:
            True if queued successfully
        """
        if not self._connected or not self.pool:
            logger.error("Not connected to TimescaleDB")
            return False
        
        try:
            # Add to batch queue
            self._batch_queue.append((exchange, instrument, data))
            
            # Process batch if full or timeout reached
            if len(self._batch_queue) >= self._batch_size:
                await self._process_batch()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to queue funding rate for {exchange}:{instrument}: {e}")
            return False
    
    async def _process_batch(self) -> bool:
        """Process batched funding rate data insertions."""
        if not self._batch_queue or not self.pool:
            return True
        
        try:
            batch_data = []
            current_time = datetime.now(timezone.utc)
            
            for exchange, instrument, data in self._batch_queue:
                # Extract timestamp or use current time
                timestamp = data.get('timestamp')
                if timestamp:
                    if isinstance(timestamp, (int, float)):
                        timestamp = datetime.fromtimestamp(timestamp, tz=timezone.utc)
                    elif isinstance(timestamp, str):
                        timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                else:
                    timestamp = current_time
                
                batch_data.append((
                    timestamp, exchange, instrument,
                    data.get('funding_rate'),
                    data.get('funding_time'),
                    data.get('collection_time'),
                    self._safe_decimal(data.get('maker_fee')),
                    self._safe_decimal(data.get('taker_fee')),
                    data.get('funding_collection_hours'),
                    self._safe_decimal(data.get('open_interest')),
                    self._safe_decimal(data.get('prev_day_px')),
                    self._safe_decimal(data.get('day_ntl_vlm')),
                    self._safe_decimal(data.get('premium')),
                    self._safe_decimal(data.get('oracle_px')),
                    self._safe_decimal(data.get('mark_px')),
                    self._safe_decimal(data.get('mid_px')),
                    self._safe_decimal(data.get('day_base_vlm')),
                    json.dumps(data.get('raw_data', {}))
                ))
            
            # Batch insert
            async with self.pool.acquire() as conn:
                await conn.executemany("""
                    INSERT INTO funding_rates (
                        timestamp, exchange, instrument, funding_rate, funding_time,
                        collection_time, maker_fee, taker_fee, funding_collection_hours,
                        open_interest, prev_day_px, day_ntl_vlm, premium, oracle_px,
                        mark_px, mid_px, day_base_vlm, raw_data
                    ) VALUES (
                        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18
                    )
                """, batch_data)

            logger.debug(f"Batch inserted {len(batch_data)} funding rates")
            self._batch_queue.clear()
            return True
            
        except Exception as e:
            logger.error(f"Failed to process batch: {e}")
            return False
    
    async def flush_batch(self) -> bool:
        """Force process any pending batch data."""
        return await self._process_batch()
    
    def _safe_decimal(self, value: Any) -> Optional[float]:
        """Safely convert value to decimal/float."""
        if value is None:
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None

    async def get_exchange_fees(self, exchange: str) -> Optional[Dict[str, Any]]:
        """
        Get default exchange fees and funding collection hours.

        Args:
            exchange: Exchange name

        Returns:
            Dictionary with fee information or None if not found
        """
        if not self._connected or not self.pool:
            logger.error("Not connected to TimescaleDB")
            return None

        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT * FROM exchange_fees WHERE exchange = $1
                """, exchange)

                if row:
                    return self._row_to_dict(row)
                return None

        except Exception as e:
            logger.error(f"Failed to get exchange fees for {exchange}: {e}")
            return None
    
    async def get_funding_rate(self, exchange: str, instrument: str) -> Optional[Dict[str, Any]]:
        """
        Get latest funding rate data for an instrument.
        
        Args:
            exchange: Exchange name
            instrument: Instrument symbol
            
        Returns:
            Funding rate data or None if not found
        """
        if not self._connected or not self.pool:
            logger.error("Not connected to TimescaleDB")
            return None
        
        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT * FROM latest_funding_rates 
                    WHERE exchange = $1 AND instrument = $2
                """, exchange, instrument)
                
                if row:
                    return self._row_to_dict(row)
                return None
                
        except Exception as e:
            logger.error(f"Failed to get funding rate for {exchange}:{instrument}: {e}")
            return None
    
    async def get_all_funding_rates(self, exchange: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """
        Get all latest funding rates, optionally filtered by exchange.
        
        Args:
            exchange: Optional exchange filter
            
        Returns:
            Dictionary of funding rates keyed by exchange:instrument
        """
        if not self._connected or not self.pool:
            logger.error("Not connected to TimescaleDB")
            return {}
        
        try:
            async with self.pool.acquire() as conn:
                if exchange:
                    rows = await conn.fetch("""
                        SELECT * FROM latest_funding_rates WHERE exchange = $1
                    """, exchange)
                else:
                    rows = await conn.fetch("SELECT * FROM latest_funding_rates")
                
                result = {}
                for row in rows:
                    data = self._row_to_dict(row)
                    key = f"{data['exchange']}:{data['instrument']}"
                    result[key] = data
                
                return result
                
        except Exception as e:
            logger.error(f"Failed to get all funding rates: {e}")
            return {}
    
    def _row_to_dict(self, row) -> Dict[str, Any]:
        """Convert database row to dictionary matching KeyDB format."""
        data = dict(row)

        # Convert timestamp to Unix timestamp for compatibility
        if data.get('timestamp'):
            data['timestamp'] = data['timestamp'].timestamp()

        # Convert Decimal types to float for compatibility
        for key, value in data.items():
            if isinstance(value, Decimal):
                data[key] = float(value)

        # Parse raw_data JSON
        if data.get('raw_data'):
            try:
                data['raw_data'] = json.loads(data['raw_data'])
            except (json.JSONDecodeError, TypeError):
                data['raw_data'] = {}

        return data
    
    async def cleanup_expired_data(self) -> int:
        """
        Clean up expired funding rate data (older than retention period).
        Note: TimescaleDB handles this automatically with retention policies,
        but we provide this method for compatibility.
        
        Returns:
            Number of rows cleaned up (always 0 as TimescaleDB handles this)
        """
        if not self._connected or not self.pool:
            return 0
        
        try:
            # TimescaleDB handles cleanup automatically via retention policies
            # We could manually delete old data here if needed, but it's not necessary
            logger.debug("TimescaleDB handles data cleanup automatically via retention policies")
            return 0
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired data: {e}")
            return 0
    
    async def get_funding_rate_history(
        self, 
        exchange: str, 
        instrument: str, 
        hours: int = 24
    ) -> List[Dict[str, Any]]:
        """
        Get funding rate history for an instrument.
        
        Args:
            exchange: Exchange name
            instrument: Instrument symbol
            hours: Number of hours of history to retrieve
            
        Returns:
            List of funding rate data ordered by timestamp (newest first)
        """
        if not self._connected or not self.pool:
            logger.error("Not connected to TimescaleDB")
            return []
        
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT * FROM funding_rates 
                    WHERE exchange = $1 AND instrument = $2 
                    AND timestamp >= NOW() - INTERVAL '1 hour' * $3
                    ORDER BY timestamp DESC
                """, exchange, instrument, hours)
                
                return [self._row_to_dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to get funding rate history for {exchange}:{instrument}: {e}")
            return []
    
    async def store_contract_details(self, exchange: str, contracts: Dict[str, Dict[str, Any]]) -> bool:
        """
        Store contract details for an exchange.
        
        Args:
            exchange: Exchange name
            contracts: Dictionary of contract symbol -> contract details
            
        Returns:
            True if stored successfully
        """
        if not self._connected or not self.pool:
            logger.error("Not connected to TimescaleDB")
            return False
        
        try:
            current_time = datetime.now(timezone.utc)
            batch_data = []
            
            for symbol, details in contracts.items():
                batch_data.append((
                    current_time,
                    exchange,
                    symbol,
                    json.dumps(details)
                ))
            
            async with self.pool.acquire() as conn:
                # Create table if it doesn't exist
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS contract_details (
                        timestamp TIMESTAMPTZ NOT NULL,
                        exchange TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        details JSONB NOT NULL,
                        PRIMARY KEY (exchange, symbol)
                    )
                """)
                
                # Batch insert with upsert
                await conn.executemany("""
                    INSERT INTO contract_details (timestamp, exchange, symbol, details)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (exchange, symbol) DO UPDATE SET
                        timestamp = EXCLUDED.timestamp,
                        details = EXCLUDED.details
                """, batch_data)
            
            logger.debug(f"Stored {len(contracts)} contract details for {exchange}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to store contract details for {exchange}: {e}")
            return False
    
    async def get_contract_details(self, exchange: str, max_age_hours: int = 24) -> Dict[str, Dict[str, Any]]:
        """
        Get cached contract details for an exchange.
        
        Args:
            exchange: Exchange name
            max_age_hours: Maximum age in hours for cached data
            
        Returns:
            Dictionary of contract details or empty dict if not found/expired
        """
        if not self._connected or not self.pool:
            logger.error("Not connected to TimescaleDB")
            return {}
        
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT symbol, details, timestamp FROM contract_details 
                    WHERE exchange = $1 
                    AND timestamp >= NOW() - INTERVAL '1 hour' * $2
                """, exchange, max_age_hours)
                
                contracts = {}
                for row in rows:
                    try:
                        contracts[row['symbol']] = json.loads(row['details'])
                    except (json.JSONDecodeError, TypeError):
                        logger.warning(f"Failed to parse contract details for {exchange}:{row['symbol']}")
                
                if contracts:
                    age_hours = (datetime.now(timezone.utc) - rows[0]['timestamp']).total_seconds() / 3600
                    logger.debug(f"Retrieved {len(contracts)} cached contract details for {exchange} (age: {age_hours:.1f}h)")
                
                return contracts
                
        except Exception as e:
            logger.error(f"Failed to get contract details for {exchange}: {e}")
            return {}
