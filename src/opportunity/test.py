import asyncio
import sys
import os
from collections import defaultdict
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from decimal import Decimal

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from timescaledb_client import TimescaleDBClient
from config import TimescaleDBConfig


@dataclass
class ExchangeData:
    """Store exchange-specific data for a ticker"""
    funding_rate: float  # Normalized to hourly
    maker_fee: float
    collection_hours: int
    raw_funding_rate: float  # Original rate before normalization


@dataclass
class ArbitrageOpportunity:
    """Store arbitrage opportunity details"""
    ticker: str
    long_exchange: str
    short_exchange: str
    long_funding: float
    short_funding: float
    long_fee: float
    short_fee: float
    hourly_profit: float  # Net profit per hour
    collection_hours_long: int
    collection_hours_short: int
    
    @property
    def funding_spread(self) -> float:
        """The raw funding rate spread"""
        return self.short_funding - self.long_funding
    
    @property
    def total_fees(self) -> float:
        """Total fees for opening position"""
        return self.long_fee + self.short_fee
    
    @property
    def effective_hourly_rate(self) -> float:
        """Effective hourly rate considering collection cycles"""
        # For exchanges with 8h funding, you only get paid every 8 hours
        # So the effective hourly rate needs to account for this
        return self.hourly_profit


class FundingArbitrageAnalyzer:
    """Analyze funding rate arbitrage opportunities"""
    
    # Exchange default configurations
    EXCHANGE_DEFAULTS = {
        'kraken': {'collection_hours': 1, 'maker_fee': 0.0002},
        'hyperliquid': {'collection_hours': 1, 'maker_fee': 0.00015},
        'blofin': {'collection_hours': 8, 'maker_fee': 0.0002},
        'mexc': {'collection_hours': 8, 'maker_fee': 0.0002}
    }
    
    def __init__(self):
        self.config = TimescaleDBConfig()
        self.client = TimescaleDBClient(self.config)
    
    async def connect(self) -> bool:
        """Connect to TimescaleDB"""
        return await self.client.connect()
    
    async def disconnect(self):
        """Disconnect from TimescaleDB"""
        await self.client.disconnect()
    
    def normalize_ticker(self, exchange: str, ticker: str) -> str:
        """
        Normalize ticker names across exchanges to enable comparison.
        Returns a standardized ticker symbol.
        """
        # Remove common suffixes and normalize format
        ticker = ticker.upper()
        
        if exchange == 'blofin':
            # BLOFIN uses format like "WIF-USDT" or "WIFUSDT"
            ticker = ticker.replace('-USDT', '').replace('USDT', '')
        elif exchange == 'mexc':
            # MEXC uses format like "WIF_USDT"
            ticker = ticker.replace('_USDT', '').replace('USDT', '')
        elif exchange == 'kraken':
            # Kraken might use different formats, normalize them
            ticker = ticker.replace('USDT', '').replace('USD', '')
        elif exchange == 'hyperliquid':
            # Hyperliquid typically uses just the symbol
            ticker = ticker.replace('USDT', '').replace('USD', '')
        
        return ticker
    
    def get_exchange_data(self, exchange: str, value_json: dict) -> ExchangeData:
        """
        Extract and normalize exchange data from raw JSON.
        """
        # Get funding rate (raw)
        raw_funding = float(value_json.get('funding_rate', 0))
        
        # Get collection hours (from data or defaults)
        collection_hours = int(value_json.get('funding_collection_hours', 
                                             self.EXCHANGE_DEFAULTS[exchange]['collection_hours']))
        
        # Normalize funding rate to hourly
        hourly_funding = raw_funding / collection_hours if collection_hours > 1 else raw_funding
        
        # Get maker fee (from data or defaults)
        maker_fee = float(value_json.get('maker_fee', 
                                        self.EXCHANGE_DEFAULTS[exchange]['maker_fee']))
        
        return ExchangeData(
            funding_rate=hourly_funding,
            maker_fee=maker_fee,
            collection_hours=collection_hours,
            raw_funding_rate=raw_funding
        )
    
    def calculate_arbitrage(self, ticker: str, exchanges_data: Dict[str, ExchangeData]) -> List[ArbitrageOpportunity]:
        """
        Calculate all possible arbitrage opportunities for a ticker across exchanges.
        Returns list of profitable opportunities.
        """
        opportunities = []
        exchanges = list(exchanges_data.keys())
        
        # Try all possible exchange pairs
        for i in range(len(exchanges)):
            for j in range(i + 1, len(exchanges)):
                ex1, ex2 = exchanges[i], exchanges[j]
                data1, data2 = exchanges_data[ex1], exchanges_data[ex2]
                
                # Try both directions (ex1 long/ex2 short and vice versa)
                for long_ex, short_ex, long_data, short_data in [
                    (ex1, ex2, data1, data2),
                    (ex2, ex1, data2, data1)
                ]:
                    # Calculate hourly profit
                    # When long: you PAY the funding rate
                    # When short: you RECEIVE the funding rate
                    # So profit = funding_received - funding_paid - fees
                    
                    funding_received = short_data.funding_rate  # From short position
                    funding_paid = long_data.funding_rate      # From long position
                    
                    # Gross profit per hour (before fees)
                    gross_hourly = funding_received - funding_paid
                    
                    # Net profit per hour (after fees)
                    # Fees are one-time for opening positions, so amortize over time
                    # For simplicity, we'll consider them as hourly impact
                    # In reality, you'd hold the position for multiple funding periods
                    total_fees = long_data.maker_fee + short_data.maker_fee
                    
                    # For a more realistic calculation, assume position held for at least
                    # one funding cycle of the slower exchange
                    max_collection = max(long_data.collection_hours, short_data.collection_hours)
                    amortized_hourly_fees = total_fees / max_collection
                    
                    net_hourly = gross_hourly - amortized_hourly_fees
                    
                    if net_hourly > 0:
                        opportunities.append(ArbitrageOpportunity(
                            ticker=ticker,
                            long_exchange=long_ex,
                            short_exchange=short_ex,
                            long_funding=long_data.funding_rate,
                            short_funding=short_data.funding_rate,
                            long_fee=long_data.maker_fee,
                            short_fee=short_data.maker_fee,
                            hourly_profit=net_hourly,
                            collection_hours_long=long_data.collection_hours,
                            collection_hours_short=short_data.collection_hours
                        ))
        
        return opportunities
    
    async def find_opportunities(self) -> List[ArbitrageOpportunity]:
        """
        Find all funding arbitrage opportunities across exchanges.
        """
        # Get all latest funding rates
        all_rates = await self.client.get_all_funding_rates()
        
        # Organize by normalized ticker
        ticker_data = defaultdict(dict)  # {ticker: {exchange: ExchangeData}}
        
        for key, value_json in all_rates.items():
            if ':' not in key:
                continue
                
            exchange, raw_ticker = key.split(':', 1)
            
            # Skip if funding rate is 0 (usually means halted trading)
            if float(value_json.get('funding_rate', 0)) == 0:
                continue
            
            # Normalize ticker for comparison
            normalized_ticker = self.normalize_ticker(exchange, raw_ticker)
            
            # Get exchange data
            exchange_data = self.get_exchange_data(exchange, value_json)
            
            ticker_data[normalized_ticker][exchange] = exchange_data
        
        # Find arbitrage opportunities
        all_opportunities = []
        
        for ticker, exchanges_data in ticker_data.items():
            # Need at least 2 exchanges for arbitrage
            if len(exchanges_data) < 2:
                continue
            
            opportunities = self.calculate_arbitrage(ticker, exchanges_data)
            all_opportunities.extend(opportunities)
        
        # Sort by hourly profit (best first)
        all_opportunities.sort(key=lambda x: x.hourly_profit, reverse=True)
        
        return all_opportunities
    
    def format_percentage(self, value: float, decimals: int = 4) -> str:
        """Format a decimal as percentage string"""
        return f"{value * 100:.{decimals}f}%"
    
    def print_opportunities(self, opportunities: List[ArbitrageOpportunity], top_n: int = 20):
        """
        Print opportunities in a clean, formatted table.
        """
        print(f"\n{'='*120}")
        print(f"FUNDING ARBITRAGE OPPORTUNITIES - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*120}\n")
        
        if not opportunities:
            print("No profitable arbitrage opportunities found.")
            return
        
        # Header
        print(f"{'#':<3} {'Ticker':<10} {'Strategy':<25} {'1H Profit':<12} {'Spread':<12} {'Fees':<12} {'Details'}")
        print(f"{'-'*3} {'-'*10} {'-'*25} {'-'*12} {'-'*12} {'-'*12} {'-'*40}")
        
        for i, opp in enumerate(opportunities[:top_n], 1):
            # Format strategy
            strategy = f"{opp.short_exchange}↓ / {opp.long_exchange}↑"
            
            # Format rates
            hourly_profit = self.format_percentage(opp.hourly_profit)
            funding_spread = self.format_percentage(opp.funding_spread)
            total_fees = self.format_percentage(opp.total_fees)
            
            # Details about funding rates and collection
            long_detail = f"L:{self.format_percentage(opp.long_funding, 3)}@{opp.collection_hours_long}h"
            short_detail = f"S:{self.format_percentage(opp.short_funding, 3)}@{opp.collection_hours_short}h"

            # Depending on if funding is positive, we need to add spaces
            long_detail = long_detail.replace('L:', 'L: ') if opp.long_funding > 0 else long_detail
            short_detail = short_detail.replace('S:', 'S: ') if opp.short_funding > 0 else short_detail
            
            print(f"{i:<3} {opp.ticker:<10} {strategy:<25} {hourly_profit:<12} {funding_spread:<12} {total_fees:<12} {long_detail} | {short_detail}")
        
        print(f"\n{'='*120}")
        print(f"Total opportunities found: {len(opportunities)}")
        print(f"Legend: ↓=Short position (receive funding) | ↑=Long position (pay funding)")
        print(f"        @Xh=Funding collected every X hours | 1H Profit=Net hourly profit after fees")
        print(f"{'='*120}\n")


async def main():
    """Main execution loop"""
    analyzer = FundingArbitrageAnalyzer()
    
    if not await analyzer.connect():
        print("Failed to connect to TimescaleDB")
        return
    
    try:
        while True:
            try:
                # Find and display opportunities
                opportunities = await analyzer.find_opportunities()
                
                # Clear screen for clean display (optional)
                # os.system('cls' if os.name == 'nt' else 'clear')
                
                analyzer.print_opportunities(opportunities, top_n=25)
                
                # Show summary statistics
                if opportunities:
                    avg_profit = sum(o.hourly_profit for o in opportunities) / len(opportunities)
                    max_profit = opportunities[0].hourly_profit if opportunities else 0
                    
                    print(f"\nSummary Statistics:")
                    print(f"  • Best opportunity: {analyzer.format_percentage(max_profit)} per hour")
                    print(f"  • Average profit: {analyzer.format_percentage(avg_profit)} per hour")
                    print(f"  • Opportunities > 0.01%/h: {sum(1 for o in opportunities if o.hourly_profit > 0.0001)}")
                    print(f"  • Opportunities > 0.05%/h: {sum(1 for o in opportunities if o.hourly_profit > 0.0005)}")
                
                print(f"\nRefreshing in 60 seconds...")
                
            except Exception as e:
                print(f"Error in analysis loop: {e}")
                import traceback
                traceback.print_exc()
            
            await asyncio.sleep(60)
            
    except KeyboardInterrupt:
        print("\n\nShutting down...")
    finally:
        await analyzer.disconnect()


if __name__ == '__main__':
    asyncio.run(main())