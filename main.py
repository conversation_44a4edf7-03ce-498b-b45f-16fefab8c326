"""Main entry point for funding arbitrage data feeds."""

import asyncio
import sys
import os
import logging

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.run_new_pipeline import main


if __name__ == "__main__":
    # Set log level to debug
    logging.basicConfig()
    # logging.getLogger().setLevel(logging.DEBUG)
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
