#!/usr/bin/env python3
"""Initialize TimescaleDB database schema for funding arbitrage bot."""

import asyncio
import asyncpg
import sys
import os
from typing import Optional

# Database connection settings
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'user': 'postgres',
    'password': 'fundingarb',
    'database': 'postgres'  # Connect to default database first
}

FUNDINGARB_DB = 'fundingarb'

# SQL to create the database
CREATE_DATABASE_SQL = f"""
CREATE DATABASE {FUNDINGARB_DB};
"""

# Ensure TimescaleDB extension is available in the target DB
CREATE_EXTENSION_SQL = """
CREATE EXTENSION IF NOT EXISTS timescaledb;
"""

# SQL to optionally drop all objects (views, tables)
DROP_ALL_SQL = """
DROP VIEW IF EXISTS latest_funding_rates CASCADE;
DROP VIEW IF EXISTS funding_rates CASCADE;
DROP TABLE IF EXISTS funding_snapshots CASCADE;
DROP TABLE IF EXISTS funding_schedule CASCADE;
DROP TABLE IF EXISTS instrument_fees CASCADE;
DROP TABLE IF EXISTS instruments CASCADE;
DROP TABLE IF EXISTS products CASCADE;
DROP TABLE IF EXISTS exchanges CASCADE;
DROP TABLE IF EXISTS contract_details CASCADE;
"""

# Core relational schema
CREATE_EXCHANGES_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS exchanges (
    id SERIAL PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    default_maker_fee DECIMAL(10, 8),
    default_taker_fee DECIMAL(10, 8),
    default_funding_collection_hours INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
"""

CREATE_PRODUCTS_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS products (
    id SERIAL PRIMARY KEY,
    base_asset TEXT NOT NULL,
    quote_asset TEXT NOT NULL,
    symbol TEXT GENERATED ALWAYS AS (base_asset || '-' || quote_asset) STORED UNIQUE
);
"""

CREATE_INSTRUMENTS_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS instruments (
    id SERIAL PRIMARY KEY,
    exchange_id INTEGER NOT NULL REFERENCES exchanges(id) ON DELETE CASCADE,
    product_id INTEGER REFERENCES products(id) ON DELETE RESTRICT,
    exchange_symbol TEXT NOT NULL,
    contract_type TEXT,
    settle_currency TEXT,
    contract_value DECIMAL(30, 12),
    min_size DECIMAL(30, 12),
    lot_size DECIMAL(30, 12),
    tick_size DECIMAL(30, 12),
    price_scale INTEGER,
    amount_scale INTEGER,
    max_leverage DECIMAL(10, 2),
    status TEXT,
    UNIQUE(exchange_id, exchange_symbol)
);
"""

CREATE_INSTRUMENT_FEES_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS instrument_fees (
    instrument_id INTEGER PRIMARY KEY REFERENCES instruments(id) ON DELETE CASCADE,
    maker_fee DECIMAL(10, 8),
    taker_fee DECIMAL(10, 8),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
"""

CREATE_FUNDING_SCHEDULE_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS funding_schedule (
    instrument_id INTEGER PRIMARY KEY REFERENCES instruments(id) ON DELETE CASCADE,
    collection_hours INTEGER,
    next_settle_time BIGINT,
    max_funding_rate DECIMAL(10, 8),
    min_funding_rate DECIMAL(10, 8),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
"""

CREATE_FUNDING_SNAPSHOTS_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS funding_snapshots (
    timestamp TIMESTAMPTZ NOT NULL,
    exchange_id INTEGER NOT NULL REFERENCES exchanges(id) ON DELETE CASCADE,
    instrument_id INTEGER NOT NULL REFERENCES instruments(id) ON DELETE CASCADE,
    funding_rate DECIMAL(20, 15) NOT NULL,
    funding_time BIGINT,
    mark_px DECIMAL(20, 8),
    mid_px DECIMAL(20, 8),
    oracle_px DECIMAL(20, 8),
    premium DECIMAL(20, 8),
    bid_px DECIMAL(20, 8),
    ask_px DECIMAL(20, 8),
    bid_size DECIMAL(30, 8),
    ask_size DECIMAL(30, 8),
    open_interest DECIMAL(30, 8),
    volume_24h DECIMAL(30, 8),
    volume_quote DECIMAL(30, 8),
    prev_day_px DECIMAL(20, 8),
    impact_bid_px DECIMAL(20, 8),
    impact_ask_px DECIMAL(20, 8),
    raw_data JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (timestamp, exchange_id, instrument_id)
);
"""

# Keep contract_details as a raw cache table for now
CREATE_CONTRACT_DETAILS_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS contract_details (
    timestamp TIMESTAMPTZ NOT NULL,
    exchange TEXT NOT NULL,
    symbol TEXT NOT NULL,
    details JSONB NOT NULL,
    PRIMARY KEY (exchange, symbol)
);
"""

# SQL to create hypertable (TimescaleDB specific)
CREATE_HYPERTABLE_SQL = """
SELECT create_hypertable('funding_snapshots', 'timestamp', if_not_exists => TRUE);
"""

# SQL to create indexes for better query performance
CREATE_INDEXES_SQL = [
    "CREATE INDEX IF NOT EXISTS idx_instruments_exchange_symbol ON instruments (exchange_id, exchange_symbol)",
    "CREATE INDEX IF NOT EXISTS idx_funding_snapshots_timestamp ON funding_snapshots (timestamp DESC)",
    "CREATE INDEX IF NOT EXISTS idx_funding_snapshots_ex_instrument_ts ON funding_snapshots (exchange_id, instrument_id, timestamp DESC)",
    "CREATE INDEX IF NOT EXISTS idx_funding_snapshots_instrument_ts ON funding_snapshots (instrument_id, timestamp DESC)"
]

# SQL to create a view for latest funding rates (unified across exchanges)
CREATE_LATEST_VIEW_SQL = """
CREATE OR REPLACE VIEW latest_funding_rates AS
SELECT DISTINCT ON (e.name, i.exchange_symbol)
    e.name AS exchange,
    i.exchange_symbol AS instrument,
    s.funding_rate,
    s.funding_time,
    CAST(NULL AS DECIMAL(20,10)) AS collection_time,
    COALESCE(f.maker_fee, e.default_maker_fee) AS maker_fee,
    COALESCE(f.taker_fee, e.default_taker_fee) AS taker_fee,
    COALESCE(fs.collection_hours, e.default_funding_collection_hours) AS funding_collection_hours,
    s.open_interest,
    s.prev_day_px,
    s.volume_24h AS day_ntl_vlm,
    s.premium,
    s.oracle_px,
    s.mark_px,
    s.mid_px,
    CAST(NULL AS DECIMAL(30,8)) AS day_base_vlm,
    s.raw_data,
    s.timestamp,
    s.created_at
FROM funding_snapshots s
JOIN exchanges e ON e.id = s.exchange_id
JOIN instruments i ON i.id = s.instrument_id
LEFT JOIN instrument_fees f ON f.instrument_id = i.id
LEFT JOIN funding_schedule fs ON fs.instrument_id = i.id
ORDER BY e.name, i.exchange_symbol, s.timestamp DESC;
"""

# SQL to create data retention policy (keep data for 7 days by default)
CREATE_RETENTION_POLICY_SQL = """
SELECT add_retention_policy('funding_snapshots', INTERVAL '7 days', if_not_exists => TRUE);
"""

# SQL to insert default exchanges with fees
INSERT_DEFAULT_EXCHANGES_SQL = """
INSERT INTO exchanges (name, default_maker_fee, default_taker_fee, default_funding_collection_hours) VALUES
    ('kraken', 0.0002, 0.0005, 1),
    ('hyperliquid', 0.00015, 0.0005, 1),
    ('blofin', 0.0002, 0.0005, 8),
    ('mexc', 0.0002, 0.0006, 8)
ON CONFLICT (name) DO UPDATE SET
    default_maker_fee = EXCLUDED.default_maker_fee,
    default_taker_fee = EXCLUDED.default_taker_fee,
    default_funding_collection_hours = EXCLUDED.default_funding_collection_hours,
    updated_at = NOW();
"""


async def database_exists(connection: asyncpg.Connection, db_name: str) -> bool:
    """Check if database exists."""
    result = await connection.fetchval(
        "SELECT 1 FROM pg_database WHERE datname = $1", db_name
    )
    return result is not None


async def create_database() -> bool:
    """Create the fundingarb database if it doesn't exist."""
    try:
        # Connect to default postgres database
        conn = await asyncpg.connect(**DB_CONFIG)
        
        # Check if fundingarb database exists
        if await database_exists(conn, FUNDINGARB_DB):
            print(f"Database '{FUNDINGARB_DB}' already exists.")
        else:
            print(f"Creating database '{FUNDINGARB_DB}'...")
            await conn.execute(CREATE_DATABASE_SQL)
            print(f"Database '{FUNDINGARB_DB}' created successfully.")
        
        await conn.close()
        return True
        
    except Exception as e:
        print(f"Error creating database: {e}")
        return False


async def setup_schema(drop_all: bool = False) -> bool:
    """Set up the unified database schema in the fundingarb database."""
    try:
        # Connect to fundingarb database
        db_config = DB_CONFIG.copy()
        db_config['database'] = FUNDINGARB_DB

        conn = await asyncpg.connect(**db_config)

        if drop_all:
            print("Dropping all DB objects (views, tables)...")
            await conn.execute(DROP_ALL_SQL)

        print("Creating core tables (exchanges, products, instruments, fees, schedule, snapshots)...")
        await conn.execute(CREATE_EXCHANGES_TABLE_SQL)
        await conn.execute(CREATE_PRODUCTS_TABLE_SQL)
        await conn.execute(CREATE_INSTRUMENTS_TABLE_SQL)
        await conn.execute(CREATE_INSTRUMENT_FEES_TABLE_SQL)
        await conn.execute(CREATE_FUNDING_SCHEDULE_TABLE_SQL)
        await conn.execute(CREATE_FUNDING_SNAPSHOTS_TABLE_SQL)
        await conn.execute(CREATE_CONTRACT_DETAILS_TABLE_SQL)

        print("Creating TimescaleDB hypertable...")
        await conn.execute(CREATE_HYPERTABLE_SQL)

        print("Creating indexes...")
        for index_sql in CREATE_INDEXES_SQL:
            await conn.execute(index_sql)

        print("Creating latest funding rates view and compatibility views...")
        # Compatibility view for exchange_fees queried by code
        await conn.execute("""
        CREATE OR REPLACE VIEW exchange_fees AS
        SELECT name AS exchange,
               default_maker_fee,
               default_taker_fee,
               default_funding_collection_hours AS funding_collection_hours,
               created_at,
               updated_at
        FROM exchanges;
        """)

        # Compatibility view for historical funding_rates (select-only)
        await conn.execute("""
        CREATE OR REPLACE VIEW funding_rates AS
        SELECT
            s.timestamp,
            e.name AS exchange,
            i.exchange_symbol AS instrument,
            s.funding_rate,
            s.funding_time,
            NULL::DECIMAL(20,10) AS collection_time,
            COALESCE(f.maker_fee, e.default_maker_fee) AS maker_fee,
            COALESCE(f.taker_fee, e.default_taker_fee) AS taker_fee,
            COALESCE(fs.collection_hours, e.default_funding_collection_hours) AS funding_collection_hours,
            s.open_interest,
            s.prev_day_px,
            s.volume_24h AS day_ntl_vlm,
            s.premium,
            s.oracle_px,
            s.mark_px,
            s.mid_px,
            NULL::DECIMAL(30,8) AS day_base_vlm,
            s.raw_data,
            s.created_at
        FROM funding_snapshots s
        JOIN exchanges e ON e.id = s.exchange_id
        JOIN instruments i ON i.id = s.instrument_id
        LEFT JOIN instrument_fees f ON f.instrument_id = i.id
        LEFT JOIN funding_schedule fs ON fs.instrument_id = i.id;
        """)

        # Unified latest view
        await conn.execute(CREATE_LATEST_VIEW_SQL)

        print("Setting up data retention policy...")
        await conn.execute(CREATE_RETENTION_POLICY_SQL)

        print("Seeding default exchanges and fees...")
        await conn.execute(INSERT_DEFAULT_EXCHANGES_SQL)

        # Upsert trigger to keep old client inserts working via funding_rates view
        print("Creating insert trigger on funding_rates view for compatibility...")
        await conn.execute("""
        CREATE OR REPLACE FUNCTION trg_insert_funding_rates_fn()
        RETURNS trigger AS $$
        DECLARE v_exchange_id INTEGER;
                v_instrument_id INTEGER;
        BEGIN
            -- Upsert exchange
            INSERT INTO exchanges (name, default_maker_fee, default_taker_fee, default_funding_collection_hours)
            VALUES (NEW.exchange, NEW.maker_fee, NEW.taker_fee, NEW.funding_collection_hours)
            ON CONFLICT (name) DO UPDATE SET
                default_maker_fee = COALESCE(EXCLUDED.default_maker_fee, exchanges.default_maker_fee),
                default_taker_fee = COALESCE(EXCLUDED.default_taker_fee, exchanges.default_taker_fee),
                default_funding_collection_hours = COALESCE(EXCLUDED.default_funding_collection_hours, exchanges.default_funding_collection_hours),
                updated_at = NOW()
            RETURNING id INTO v_exchange_id;

            -- Upsert instrument (product unknown here)
            INSERT INTO instruments (exchange_id, exchange_symbol)
            VALUES (v_exchange_id, NEW.instrument)
            ON CONFLICT (exchange_id, exchange_symbol) DO UPDATE SET exchange_symbol = EXCLUDED.exchange_symbol
            RETURNING id INTO v_instrument_id;

            -- Upsert instrument fees when provided
            IF NEW.maker_fee IS NOT NULL OR NEW.taker_fee IS NOT NULL THEN
                INSERT INTO instrument_fees (instrument_id, maker_fee, taker_fee)
                VALUES (v_instrument_id, NEW.maker_fee, NEW.taker_fee)
                ON CONFLICT (instrument_id) DO UPDATE SET
                    maker_fee = COALESCE(EXCLUDED.maker_fee, instrument_fees.maker_fee),
                    taker_fee = COALESCE(EXCLUDED.taker_fee, instrument_fees.taker_fee),
                    updated_at = NOW();
            END IF;

            -- Upsert funding schedule bits when provided
            IF NEW.funding_collection_hours IS NOT NULL OR NEW.funding_time IS NOT NULL THEN
                INSERT INTO funding_schedule (instrument_id, collection_hours, next_settle_time)
                VALUES (v_instrument_id, NEW.funding_collection_hours, NEW.funding_time)
                ON CONFLICT (instrument_id) DO UPDATE SET
                    collection_hours = COALESCE(EXCLUDED.collection_hours, funding_schedule.collection_hours),
                    next_settle_time = COALESCE(EXCLUDED.next_settle_time, funding_schedule.next_settle_time),
                    updated_at = NOW();
            END IF;

            -- Insert snapshot
            INSERT INTO funding_snapshots (
                timestamp, exchange_id, instrument_id, funding_rate, funding_time,
                mark_px, mid_px, oracle_px, premium, bid_px, ask_px, bid_size, ask_size,
                open_interest, volume_24h, volume_quote, prev_day_px, impact_bid_px, impact_ask_px, raw_data
            ) VALUES (
                COALESCE(NEW.timestamp, NOW()), v_exchange_id, v_instrument_id, NEW.funding_rate, NEW.funding_time,
                NEW.mark_px, NEW.mid_px, NEW.oracle_px, NEW.premium, NULL, NULL, NULL, NULL,
                NEW.open_interest, NEW.day_ntl_vlm, NULL, NEW.prev_day_px, NULL, NULL,
                COALESCE(CASE WHEN pg_typeof(NEW.raw_data) = 'jsonb'::regtype THEN NEW.raw_data ELSE to_jsonb(NEW.raw_data) END, '{}'::jsonb)
            )
            ON CONFLICT (timestamp, exchange_id, instrument_id) DO NOTHING;

            RETURN NULL; -- since this is an INSTEAD OF trigger
        END;
        $$ LANGUAGE plpgsql;

        DROP TRIGGER IF EXISTS trg_insert_funding_rates ON funding_rates;
        CREATE TRIGGER trg_insert_funding_rates
        INSTEAD OF INSERT ON funding_rates
        FOR EACH ROW EXECUTE FUNCTION trg_insert_funding_rates_fn();
        """);

        await conn.close()
        print("Database schema setup completed successfully!")
        return True

    except Exception as e:
        print(f"Error setting up schema: {e}")
        return False


async def test_connection() -> bool:
    """Test connection to the fundingarb database."""
    try:
        db_config = DB_CONFIG.copy()
        db_config['database'] = FUNDINGARB_DB

        conn = await asyncpg.connect(**db_config)

        # Test basic query
        result = await conn.fetchval("SELECT NOW()")
        print(f"Connection test successful. Current time: {result}")

        # Test core table and views exist
        snapshots_exists = await conn.fetchval(
            "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'funding_snapshots')"
        )
        latest_view_exists = await conn.fetchval(
            "SELECT EXISTS (SELECT FROM information_schema.views WHERE table_name = 'latest_funding_rates')"
        )
        print(f"funding_snapshots table exists: {snapshots_exists}; latest_funding_rates view exists: {latest_view_exists}")

        await conn.close()
        return True

    except Exception as e:
        print(f"Connection test failed: {e}")
        return False


async def main():
    """Main function to initialize TimescaleDB."""
    print("Initializing TimescaleDB for funding arbitrage bot...")
    print("=" * 50)

    drop_all = False
    if len(sys.argv) > 1 and sys.argv[1] in {"--drop-all", "-D"}:
        drop_all = True
        print("Option: Drop-all enabled. Will drop and recreate all DB objects.")

    # Step 1: Create database
    if not await create_database():
        print("Failed to create database. Exiting.")
        return 1

    # Step 1.5: Ensure extension exists in target DB
    try:
        db_config = DB_CONFIG.copy()
        db_config['database'] = FUNDINGARB_DB
        conn = await asyncpg.connect(**db_config)
        await conn.execute(CREATE_EXTENSION_SQL)
        await conn.close()
    except Exception as e:
        print(f"Warning: Failed to create TimescaleDB extension: {e}")

    # Step 2: Setup schema
    if not await setup_schema(drop_all=drop_all):
        print("Failed to setup schema. Exiting.")
        return 1

    # Step 3: Test connection
    if not await test_connection():
        print("Connection test failed. Exiting.")
        return 1

    print("=" * 50)
    print("TimescaleDB initialization completed successfully!")
    print(f"Database: {FUNDINGARB_DB}")
    print("You can now run your funding arbitrage bot with TimescaleDB.")

    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
